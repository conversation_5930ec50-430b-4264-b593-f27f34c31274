<template>
  <PhantomLayout title="Generate Session Codes">
    <!-- Main Content -->
    <div class="w-full max-w-3xl mx-auto p-6">
      <section>
        <div class="flex justify-end mb-6">
          <button
            class="btn-phantom-secondary px-4 py-2 text-sm"
            @click="navigateTo('/sessions')"
          >
            <span class="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M10 19l-7-7m0 0l7-7m-7 7h18"
                />
              </svg>
              Back to Sessions
            </span>
          </button>
        </div>
        <form
          class="space-y-6"
          @submit.prevent="generateSessions"
        >
          <!-- Assessment Selection -->
          <div>
            <label
              for="assessmentSelect"
              class="block text-white font-medium mb-2"
            >Select Assessment</label>
            <select
              id="assessmentSelect"
              v-model="selectedAssessmentId"
              name="assessmentSelect"
              autocomplete="off"
              class="w-full bg-gray-800 border border-white/10 rounded-lg px-4 py-2.5 text-white focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50 [&>option]:bg-gray-800 [&>option]:text-white"
              required
            >
              <option
                value=""
                disabled
              >
                Select an assessment
              </option>
              <option
                v-for="assessment in assessments"
                :key="assessment.id_hash || assessment.id"
                :value="assessment.id_hash || assessment.id"
              >
                {{ assessment.name }}
              </option>
            </select>
          </div>

          <!-- Usernames or Emails -->
          <div>
            <label
              for="usernames"
              class="block text-white font-medium mb-2"
            >Usernames or Emails (comma-separated)</label>
            <textarea
              id="usernames"
              v-model="usernames"
              name="usernames"
              autocomplete="off"
              class="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2.5 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50 h-24"
              placeholder="e.g. user1,user2,user3 or <EMAIL>,<EMAIL>"
              required
            />
            <p class="text-xs text-white/60 mt-1">
              Enter usernames or email addresses separated by commas (no
              spaces). For emails, the username part (before @) will be used as
              the display name.
            </p>
          </div>

          <!-- Loading indicator -->
          <div
            v-if="isLoading"
            class="flex justify-center items-center py-4"
          >
            <div
              class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-phantom-blue"
            />
            <span class="ml-3 text-white">Generating session codes...</span>
          </div>

          <!-- Error/Success message -->
          <div
            v-if="message"
            class="my-4"
          >
            <div
              :class="
                isSuccess
                  ? 'border-green-500 text-green-300'
                  : 'border-red-500 text-red-300'
              "
              class="border-l-4 pl-4 py-3"
            >
              {{ message }}
            </div>
          </div>

          <!-- Results table -->
          <div
            v-if="sessions.length > 0"
            class="space-y-4"
          >
            <!-- Sessions table -->
            <div class="overflow-x-auto">
              <table class="w-full text-left border-collapse">
                <thead>
                  <tr class="border-b border-white/10">
                    <th class="py-3 px-4 text-white/80 font-medium">
                      Username
                    </th>
                    <th class="py-3 px-4 text-white/80 font-medium">
                      Session Code
                    </th>
                    <th class="py-3 px-4 text-white/80 font-medium">
                      Session ID
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="(session, index) in sessions"
                    :key="index"
                    class="border-b border-white/5 hover:bg-white/5 transition-colors"
                  >
                    <td class="py-3 px-4 text-white">
                      {{ session.username }}
                    </td>
                    <td class="py-3 px-4">
                      <span
                        class="font-mono bg-phantom-blue/10 text-phantom-blue px-3 py-1 rounded"
                      >{{ getDisplaySessionCode(session) }}</span>
                    </td>
                    <td class="py-3 px-4 text-white/80">
                      {{ session.sessionDbId }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Generated Link Display -->
            <div
              v-if="generatedLink"
              class="mt-8 pt-8 border-t border-white/10"
            >
              <h3
                class="text-lg font-semibold text-white mb-4 flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-phantom-blue"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                  />
                </svg>
                Generated Quiz Link
              </h3>

              <div
                class="mb-4 bg-phantom-blue/5 p-4 rounded border-l-4 border-phantom-blue/30"
              >
                <p class="text-white/80 text-sm mb-2">
                  Share this link with users to take the quiz:
                </p>
                <div class="flex items-center space-x-2">
                  <input
                    type="text"
                    :value="generatedLink"
                    readonly
                    class="flex-1 px-3 py-2 bg-white/5 rounded text-white text-sm focus:outline-none"
                  >
                  <button
                    class="flex-shrink-0 text-xs text-white/80 bg-phantom-blue/10 hover:bg-phantom-blue/20 rounded p-2 transition-colors"
                    @click="copyToClipboard"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <div class="text-sm text-white/70">
                <p>
                  <strong class="text-white/90">Assessment:</strong>
                  {{ selectedAssessmentName }}
                </p>
                <p>
                  <strong class="text-white/90">Generated Sessions:</strong>
                  {{ sessions.length }}
                </p>
                <p>
                  <strong class="text-white/90">Generated:</strong>
                  {{ new Date().toLocaleString() }}
                </p>
              </div>
            </div>
          </div>

          <!-- Submit button -->
          <div class="flex justify-between">
            <button
              type="submit"
              :disabled="isLoading"
              class="btn-phantom px-6 py-3"
            >
              <span class="flex items-center">
                <svg
                  v-if="isLoading"
                  class="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  />
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                <svg
                  v-else
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1 1 21 9z"
                  />
                </svg>
                {{ isLoading ? "Generating..." : "Generate Codes" }}
              </span>
            </button>
          </div>
        </form>
      </section>
    </div>
  </PhantomLayout>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { api } from "@/services/api";
import { getErrorMessage, logError } from "@/utils/errorHandling";
import { useMessageHandler } from "@/utils/messageHandler";
import {
  extractResponseData,
  extractErrorInfo,
} from "@/utils/apiResponseHandler";
import {
  getDisplaySessionCode,
  decodeAssessmentId,
  isHashId,
  decodeSessionCodes,
} from "@/utils/hashIds";
import {
  debug,
  info,
  warning,
  error,
  logApiRequest,
  logApiResponse,
  logUserAction,
} from "@/utils/logger";
import {
  useNavigation,
  useLoadingState
} from "@/composables";
import PhantomLayout from "@/components/layout/Layout.vue";

// Composables
const navigation = useNavigation();
const loadingState = useLoadingState();

// Message handling
const { message, isSuccess, setErrorMessage, setSuccessMessage, clearMessage } =
  useMessageHandler();

// Form data
const selectedAssessmentId = ref("");
const usernames = ref("");
const assessments = ref([]);
const sessions = ref([]);
const generatedLink = ref(""); // Store the generated quiz link

// Aliases for backward compatibility
const isLoading = loadingState.isLoading;
const navigateTo = navigation.navigateTo;

// Computed properties
const selectedAssessment = computed(() => {
  return assessments.value.find((a) => a.id == selectedAssessmentId.value);
});

const selectedAssessmentName = computed(() => {
  return selectedAssessment.value
    ? `${selectedAssessment.value.id}: ${selectedAssessment.value.name}`
    : "";
});

// Fetch assessments from API
const fetchAssessments = async () => {
  try {
    isLoading.value = true;
    clearMessage();

    // Fetch all assessments with maximum limit (backend allows up to 100 per request)
    const response = await api.admin.getAssessments({
      limit: 100,
      offset: 0,
    });

    const data = extractResponseData(response);

    // Handle different response formats
    if (data) {
      if (Array.isArray(data)) {
        assessments.value = data;
      } else if (data.assessments) {
        assessments.value = data.assessments;
      } else {
        assessments.value = [];
      }
    } else {
      assessments.value = [];
    }

    // Filter out any invalid assessments
    assessments.value = assessments.value.filter(
      (assessment) =>
        assessment &&
        typeof assessment === "object" &&
        ((assessment.id !== undefined && assessment.id !== null) ||
          (assessment.id_hash !== undefined && assessment.id_hash !== null)),
    );

    // If we have no assessments after filtering, show a message
    if (assessments.value.length === 0) {
      setErrorMessage("No assessments found. Please create assessments first.");
    }
  } catch (error) {
    logError(error, "fetchAssessments");
    setErrorMessage(getErrorMessage(error, "Failed to fetch assessments"));
    // Ensure assessments is always an array, even on error
    assessments.value = [];
  } finally {
    isLoading.value = false;
  }
};

// Generate sessions via API
const generateSessions = async () => {
  if (!selectedAssessmentId.value || !usernames.value) {
    setErrorMessage("Please fill in all required fields");
    return;
  }

  isLoading.value = true;
  clearMessage();
  sessions.value = [];
  generatedLink.value = "";

  try {
    // Validate usernames format
    const usernameList = usernames.value
      .split(",")
      .map((username) => username.trim())
      .filter((username) => username);
    if (usernameList.length === 0) {
      throw new Error("Please enter at least one username");
    }

    // Additional validation for usernames or emails
    const invalidUsernames = [];
    for (const username of usernameList) {
      if (username.length < 2 || username.length > 100) {
        invalidUsernames.push(username);
      } else if (username.includes("@")) {
        // Email validation
        if (
          !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(username)
        ) {
          invalidUsernames.push(username);
        }
      } else if (!/^[a-zA-Z0-9._-]+$/.test(username)) {
        // Username validation (non-email)
        invalidUsernames.push(username);
      }
    }

    if (invalidUsernames.length > 0) {
      throw new Error(
        `Invalid entries: ${invalidUsernames.join(", ")}. Usernames must be 2-50 characters with only letters, numbers, underscore, dash, or dot. Emails must be in valid format.`,
      );
    }

    // Check for duplicates
    const uniqueUsernames = [...new Set(usernameList)];
    if (uniqueUsernames.length !== usernameList.length) {
      throw new Error("Duplicate usernames are not allowed");
    }

    // Validate and decode assessment ID
    let assessmentId;
    if (isHashId(selectedAssessmentId.value)) {
      // If it's a hashed ID, decode it to get the original integer ID
      debug("Decoding hashed assessment ID", {
        hashedId: selectedAssessmentId.value,
      });
      assessmentId = await decodeAssessmentId(selectedAssessmentId.value);
      debug("Decoded assessment ID", { assessmentId });
      if (!assessmentId) {
        throw new Error("Failed to decode assessment ID");
      }
    } else {
      // If it's already an integer ID, parse it
      assessmentId = parseInt(selectedAssessmentId.value);
      debug("Using integer assessment ID", { assessmentId });
      if (isNaN(assessmentId) || assessmentId <= 0) {
        throw new Error("Invalid assessment selected");
      }
    }

    // Call the API to generate sessions
    const response = await api.admin.createSession({
      assessment_id: assessmentId,
      usernames: usernames.value, // API expects comma-separated string
    });

    const responseData = extractResponseData(response);

    // Validate response
    if (!responseData) {
      throw new Error("Invalid response from server");
    }

    const rawSessions = responseData.sessions || [];

    // Decode session codes for display
    sessions.value = await decodeSessionCodes(rawSessions);

    // Handle warnings if any sessions failed
    let messageText =
      responseData.message ||
      `Successfully generated ${sessions.value.length} session codes!`;
    if (responseData.warnings) {
      messageText += `\n\nWarnings: ${responseData.warnings}`;
    }

    setSuccessMessage(messageText);

    // Clear the form on successful generation
    usernames.value = "";

    // Generate quiz link automatically
    if (sessions.value.length > 0) {
      await generateQuizLink(assessmentId);
    }
  } catch (error) {
    logError(error, "generateSessions");
    setErrorMessage(
      getErrorMessage(
        error,
        "An unexpected error occurred while generating sessions",
      ),
    );
  } finally {
    isLoading.value = false;
  }
};

// Generate quiz link for the assessment
const generateQuizLink = async (assessmentId) => {
  try {
    const response = await api.admin.generateLink({
      assessment_id: assessmentId,
    });

    const responseData = extractResponseData(response);
    generatedLink.value = responseData.link;
    setSuccessMessage(
      "Sessions and quiz link generated successfully! You can view all pending sessions in the Sessions List.",
    );
  } catch (error) {
    logError(error, "generateQuizLink");
    setErrorMessage(
      getErrorMessage(
        error,
        "Sessions were created, but failed to generate quiz link",
      ),
    );
    generatedLink.value = "";
  }
};

// Copy link to clipboard
const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(generatedLink.value);
    setSuccessMessage("Link copied to clipboard!");
  } catch (error) {
    logError(error, "copyToClipboard");
    setErrorMessage("Failed to copy to clipboard");
  }
};

onMounted(() => {
  fetchAssessments();
});
</script>

<style scoped>
/* No need for animation styles as they're now in HerbitBackground component */
</style>
