<template>
  <PhantomLayout title="Reports">
    <div class="p-6 -mt-10">
      <!-- Tabs Container -->
      <div class="mb-8">
        <div
          class="flex space-x-1 bg-white/5 backdrop-blur-sm p-1 rounded-xl border border-white/10"
        >
          <button
            v-for="tab in tabs"
            :key="tab.value"
            class="flex-1 py-2.5 px-4 rounded-lg text-sm font-medium transition-all duration-200"
            :class="
              activeTab === tab.value
                ? `bg-gradient-to-r ${tab.gradient} text-white shadow-sm`
                : 'text-white/70 hover:text-white hover:bg-white/5'
            "
            @click="activeTab = tab.value"
          >
            {{ tab.label }}
          </button>
        </div>
      </div>

      <!-- Skillwise Report Tab Content -->
      <div
        v-if="activeTab === 'skill-wise'"
        class="card-phantom"
      >
        <SkillwiseReport />
      </div>

      <!-- Assessment-wise Report Tab Content -->
      <div
        v-if="activeTab === 'assessment-wise'"
        class="card-phantom"
      >
        <AssessmentWiseReport />
      </div>

      <!-- User-wise Report Tab Content -->
      <div
        v-if="activeTab === 'user-wise'"
        class="card-phantom"
      >
        <UserWiseReport />
      </div>
    </div>
  </PhantomLayout>
</template>

<script setup>
import { onMounted } from 'vue';
import PhantomLayout from '@/components/layout/Layout.vue';
import SkillwiseReport from '@/components/reports/SkillWiseReport.vue';
import AssessmentWiseReport from '@/components/reports/AssessmentWiseReport.vue';
import UserWiseReport from '@/components/reports/UserWiseReport.vue';
import { useTabs } from '@/composables';

// Tabs configuration
const tabs = [
  {
    label: "Skill-wise Report",
    value: "skill-wise",
    gradient: "from-phantom-blue to-phantom-indigo",
  },
  {
    label: "Assessment-wise Report",
    value: "assessment-wise",
    gradient: "from-phantom-indigo to-phantom-purple",
  },
  {
    label: "User-wise Report",
    value: "user-wise",
    gradient: "from-phantom-purple to-phantom-blue",
  },
];

// Tabs management using composable
const tabsManager = useTabs("skill-wise");
const activeTab = tabsManager.activeTab;

onMounted(() => {
  // Check if we should switch to a specific tab based on URL query param
  const urlParams = new URLSearchParams(window.location.search);
  const tab = urlParams.get("tab");

  if (tab === "users") {
    activeTab.value = "user-wise";
  } else if (tab === "assessments") {
    activeTab.value = "assessment-wise";
  } else if (tab === "skills") {
    activeTab.value = "skill-wise";
  }
});
</script>

<style scoped>
/* Any additional component-specific styles */
</style>
