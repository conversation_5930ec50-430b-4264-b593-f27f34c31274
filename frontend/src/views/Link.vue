<template>
  <HerbitProfessionalLayout
    title="Generate Quiz Link"
    title-style="gradient"
    :show-home-button="true"
    :show-back-button="true"
    back-path="/"
  >
    <!-- Form Card -->
    <FormCard color="purple">
      <form
        class="space-y-6"
        @submit.prevent="generateLink"
      >
        <!-- Assessment Selection -->
        <div>
          <Label
            for="assessmentSelect"
            class="text-gray-300"
          >Select Assessment with Existing Sessions</Label>
          <select
            id="assessmentSelect"
            v-model="selectedAssessmentId"
            name="assessmentSelect"
            :disabled="assessments.length === 0"
            autocomplete="off"
            class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed [&>option]:bg-gray-800 [&>option]:text-white"
            required
          >
            <option
              value=""
              disabled
            >
              {{
                assessments.length === 0
                  ? "No assessments with sessions available"
                  : "Select an assessment"
              }}
            </option>
            <option
              v-for="assessment in assessments"
              :key="assessment.id"
              :value="assessment.id"
            >
              {{ assessment.name }} ({{ assessment.session_count }} session{{
                assessment.session_count !== 1 ? "s" : ""
              }})
            </option>
          </select>
          <p class="text-xs text-gray-400 mt-1">
            Only assessments with existing sessions are shown.
            <span
              v-if="assessments.length === 0"
              class="text-yellow-400"
            >
              Go to "Generate Session Codes" to create sessions first.
            </span>
            <span v-else>
              Create sessions first to generate links for other assessments.
            </span>
          </p>
        </div>

        <!-- Submit button -->
        <div class="flex justify-end">
          <Button
            type="submit"
            variant="sessionGenerate"
            size="skillButton"
            :disabled="isLoading || assessments.length === 0"
          >
            <span class="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                />
              </svg>
              {{
                isLoading
                  ? "Generating..."
                  : assessments.length === 0
                    ? "No Assessments Available"
                    : "Generate Link"
              }}
            </span>
          </Button>
        </div>
      </form>

      <!-- Generated Link Display -->
      <div
        v-if="generatedLink"
        class="mt-8 p-6 bg-gray-800/50 rounded-lg border border-purple-500/30"
      >
        <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2 text-purple-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
            />
          </svg>
          Generated Quiz Link
        </h3>

        <div class="bg-gray-900/70 p-4 rounded-lg border border-gray-700 mb-4">
          <p class="text-gray-300 text-sm mb-2">
            Share this link with users to take the quiz:
          </p>
          <div class="flex items-center space-x-2">
            <input
              id="generatedLink"
              name="generatedLink"
              type="text"
              :value="generatedLink"
              readonly
              autocomplete="off"
              class="flex-1 px-3 py-2 bg-gray-800 border border-gray-600 rounded text-white text-sm"
            >
            <Button
              variant="ghost"
              size="sm"
              class="flex-shrink-0 text-xs !text-gray-300 !bg-gray-700 !border !border-gray-600 hover:!border-gray-400 !shadow-none !ring-0"
              @click="copyToClipboard"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                />
              </svg>
              Copy
            </Button>
          </div>
        </div>

        <div class="text-sm text-gray-400">
          <p><strong>Assessment:</strong> {{ selectedAssessmentName }}</p>
          <p>
            <strong>Available Sessions:</strong>
            {{ selectedAssessment?.session_count || 0 }}
          </p>
          <p><strong>Generated:</strong> {{ new Date().toLocaleString() }}</p>
        </div>
      </div>

      <!-- Error/Success Message -->
      <Alert
        v-if="message"
        :variant="isSuccess ? 'default' : 'destructive'"
        class="mt-6"
        :class="isSuccess ? 'border-green-500/50 bg-green-500/10' : ''"
      >
        <AlertDescription :class="isSuccess ? 'text-green-400' : ''">
          {{
            message
          }}
        </AlertDescription>
      </Alert>
    </FormCard>
  </HerbitProfessionalLayout>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { api } from "@/services/api";
import { getErrorMessage, logError } from "@/utils/errorHandling";
import { extractResponseData } from "@/utils/apiResponseHandler";
import { useMessageHandler } from "@/utils/messageHandler";
import { safeCopyToClipboard } from "@/utils/domHelpers";
import { HerbitProfessionalLayout } from "@/components/layout";
import { FormCard } from "@/components/ui/form-card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";

// Message handling
const { message, isSuccess, setErrorMessage, setSuccessMessage, clearMessage } =
  useMessageHandler();

// Reactive data
const assessments = ref([]);
const selectedAssessmentId = ref("");
const isLoading = ref(false);
const generatedLink = ref("");

// Computed properties
const selectedAssessment = computed(() => {
  return assessments.value.find((a) => a.id == selectedAssessmentId.value);
});

const selectedAssessmentName = computed(() => {
  return selectedAssessment.value ? selectedAssessment.value.name : "";
});

// Methods
const fetchAssessments = async () => {
  try {
    const response = await api.admin.getAssessmentsWithSessions();
    const responseData = extractResponseData(response);
    assessments.value = responseData?.assessments || [];

    if (assessments.value.length === 0) {
      setErrorMessage(
        "No assessments with existing sessions found. Please create sessions first.",
      );
    }
  } catch (error) {
    logError(error, "fetchAssessments");
    setErrorMessage(getErrorMessage(error, "Failed to load assessments"));
  }
};

const generateLink = async () => {
  if (!selectedAssessmentId.value) {
    setErrorMessage("Please select an assessment");
    return;
  }

  isLoading.value = true;
  clearMessage();
  generatedLink.value = "";

  try {
    const assessmentId = parseInt(selectedAssessmentId.value);
    if (isNaN(assessmentId) || assessmentId <= 0) {
      throw new Error("Invalid assessment selected");
    }

    const response = await api.admin.generateLink({
      assessment_id: assessmentId,
    });

    const responseData = extractResponseData(response);
    generatedLink.value = responseData?.link;
    setSuccessMessage("Quiz link generated successfully!");
  } catch (error) {
    logError(error, "generateLink");
    setErrorMessage(
      getErrorMessage(
        error,
        "An unexpected error occurred while generating the link",
      ),
    );
    generatedLink.value = "";
  } finally {
    isLoading.value = false;
  }
};

const copyToClipboard = async () => {
  try {
    const success = await safeCopyToClipboard(generatedLink.value);
    if (success) {
      setSuccessMessage("Link copied to clipboard!");
    } else {
      setErrorMessage("Failed to copy to clipboard");
    }
  } catch (error) {
    logError(error, "copyToClipboard");
    setErrorMessage("Failed to copy to clipboard");
  }
};

// Lifecycle
onMounted(() => {
  fetchAssessments();
});
</script>
