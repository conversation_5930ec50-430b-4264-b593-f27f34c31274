/**
 * Question Selection Composable
 * 
 * Provides reusable logic for question selection and management
 * Used in assessment creation and editing
 */
import { ref, computed } from 'vue';
import { api } from '@/services/api';
import { useMessageHandler } from '@/utils/messageHandler';
import { useLoadingState } from './useUIState';
import { 
  extractResponseData, 
  extractErrorInfo 
} from '@/utils/apiResponseHandler';
import { 
  logApiRequest, 
  logApiResponse, 
  logApiError,
  logUserAction 
} from '@/utils/logger';

export function useQuestionSelection() {
  const loadingState = useLoadingState();
  const messageHandler = useMessageHandler();

  // Question data
  const availableQuestions = ref([]);
  const selectedQuestionIds = ref([]);
  const questionCounts = ref({
    easy: 6,
    intermediate: 6,
    advanced: 8
  });

  // Filters
  const searchQuery = ref('');
  const difficultyFilter = ref('all');

  // Computed properties
  const filteredQuestions = computed(() => {
    let filtered = availableQuestions.value;

    // Filter by search query
    if (searchQuery.value.trim()) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(q => 
        q.question?.toLowerCase().includes(query) ||
        q.skill_name?.toLowerCase().includes(query)
      );
    }

    // Filter by difficulty
    if (difficultyFilter.value !== 'all') {
      filtered = filtered.filter(q => q.level === difficultyFilter.value);
    }

    return filtered;
  });

  const selectedQuestionCount = computed(() => selectedQuestionIds.value.length);

  const totalRequiredQuestions = computed(() => 
    questionCounts.value.easy + 
    questionCounts.value.intermediate + 
    questionCounts.value.advanced
  );

  const questionCountsByLevel = computed(() => {
    const counts = { easy: 0, intermediate: 0, advanced: 0 };
    
    selectedQuestionIds.value.forEach(id => {
      const question = availableQuestions.value.find(q => q.que_id === id);
      if (question?.level) {
        counts[question.level]++;
      }
    });

    return counts;
  });

  const isSelectionValid = computed(() => {
    const counts = questionCountsByLevel.value;
    return (
      counts.easy >= questionCounts.value.easy &&
      counts.intermediate >= questionCounts.value.intermediate &&
      counts.advanced >= questionCounts.value.advanced
    );
  });

  /**
   * Fetch questions for selected skills
   */
  const fetchQuestionsForSkills = async (skillIds) => {
    if (!Array.isArray(skillIds) || skillIds.length === 0) {
      availableQuestions.value = [];
      return;
    }

    loadingState.startLoading('Loading questions...');

    try {
      const allQuestions = [];
      
      for (const skillId of skillIds) {
        try {
          logApiRequest('GET', `/skills/${skillId}/questions`);
          const response = await api.admin.getSkillQuestions(skillId);
          logApiResponse('GET', `/skills/${skillId}/questions`, response.status);
          
          const responseData = extractResponseData(response);
          
          if (responseData?.questions) {
            const questionsWithSkillName = responseData.questions.map(question => ({
              ...question,
              skill_name: responseData.skill_name || `Skill ${skillId}`
            }));
            allQuestions.push(...questionsWithSkillName);
          }
        } catch (error) {
          logApiError('GET', `/skills/${skillId}/questions`, error);
          console.warn(`Failed to fetch questions for skill ${skillId}:`, error);
        }
      }

      // Remove duplicates based on question ID
      const uniqueQuestions = allQuestions.filter(
        (question, index, self) =>
          index === self.findIndex(q => q.que_id === question.que_id)
      );

      availableQuestions.value = uniqueQuestions;
      
      logUserAction('questions_loaded', { 
        skillCount: skillIds.length,
        questionCount: uniqueQuestions.length
      });

    } catch (error) {
      logApiError('GET', '/questions', error);
      const errorInfo = extractErrorInfo(error);
      messageHandler.setErrorMessage(
        errorInfo.message || 'Failed to fetch questions'
      );
    } finally {
      loadingState.stopLoading();
    }
  };

  /**
   * Check if a question is selected
   */
  const isQuestionSelected = (questionId) => {
    return selectedQuestionIds.value.includes(questionId);
  };

  /**
   * Toggle question selection
   */
  const toggleQuestionSelection = (questionId) => {
    if (isQuestionSelected(questionId)) {
      removeQuestionFromSelection(questionId);
    } else {
      addQuestionToSelection(questionId);
    }
  };

  /**
   * Add question to selection
   */
  const addQuestionToSelection = (questionId) => {
    if (!isQuestionSelected(questionId)) {
      selectedQuestionIds.value.push(questionId);
      
      const question = availableQuestions.value.find(q => q.que_id === questionId);
      logUserAction('question_selected', { 
        questionId, 
        level: question?.level,
        skillName: question?.skill_name
      });
    }
  };

  /**
   * Remove question from selection
   */
  const removeQuestionFromSelection = (questionId) => {
    const index = selectedQuestionIds.value.indexOf(questionId);
    if (index > -1) {
      selectedQuestionIds.value.splice(index, 1);
      
      const question = availableQuestions.value.find(q => q.que_id === questionId);
      logUserAction('question_deselected', { 
        questionId, 
        level: question?.level,
        skillName: question?.skill_name
      });
    }
  };

  /**
   * Clear all selected questions
   */
  const clearQuestionSelection = () => {
    const previousCount = selectedQuestionIds.value.length;
    selectedQuestionIds.value = [];
    
    logUserAction('questions_cleared', { previousCount });
  };

  /**
   * Randomly select questions based on required counts
   */
  const selectRandomQuestions = () => {
    clearQuestionSelection();
    
    const questionsByLevel = {
      easy: availableQuestions.value.filter(q => q.level === 'easy'),
      intermediate: availableQuestions.value.filter(q => q.level === 'intermediate'),
      advanced: availableQuestions.value.filter(q => q.level === 'advanced')
    };

    // Randomly select questions for each level
    Object.keys(questionCounts.value).forEach(level => {
      const questionsForLevel = questionsByLevel[level];
      const requiredCount = questionCounts.value[level];
      
      if (questionsForLevel.length >= requiredCount) {
        const shuffled = [...questionsForLevel].sort(() => 0.5 - Math.random());
        const selected = shuffled.slice(0, requiredCount);
        selected.forEach(q => addQuestionToSelection(q.que_id));
      }
    });

    logUserAction('questions_randomly_selected', { 
      totalSelected: selectedQuestionIds.value.length,
      requiredCounts: questionCounts.value
    });
  };

  /**
   * Update question count requirements
   */
  const updateQuestionCounts = (newCounts) => {
    questionCounts.value = { ...questionCounts.value, ...newCounts };
    
    logUserAction('question_counts_updated', { newCounts });
  };

  /**
   * Reset selection state
   */
  const resetSelection = () => {
    availableQuestions.value = [];
    selectedQuestionIds.value = [];
    searchQuery.value = '';
    difficultyFilter.value = 'all';
    
    logUserAction('question_selection_reset');
  };

  return {
    // State
    availableQuestions,
    selectedQuestionIds,
    questionCounts,
    searchQuery,
    difficultyFilter,
    
    // Computed
    filteredQuestions,
    selectedQuestionCount,
    totalRequiredQuestions,
    questionCountsByLevel,
    isSelectionValid,
    
    // Loading and messaging
    ...loadingState,
    ...messageHandler,
    
    // Methods
    fetchQuestionsForSkills,
    isQuestionSelected,
    toggleQuestionSelection,
    addQuestionToSelection,
    removeQuestionFromSelection,
    clearQuestionSelection,
    selectRandomQuestions,
    updateQuestionCounts,
    resetSelection
  };
}