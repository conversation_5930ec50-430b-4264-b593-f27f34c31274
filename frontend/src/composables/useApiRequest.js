/**
 * API Request Composable
 * 
 * Provides reusable API request handling with:
 * - Loading states
 * - Error handling
 * - Data extraction
 * - Response caching
 * - Retry logic
 */
import { ref, computed } from 'vue';
import { getErrorMessage, logError } from '@/utils/errorHandling';
import { useMessageHandler } from '@/utils/messageHandler';
import { 
  extractResponseData, 
  extractErrorInfo,
  extractPaginationMeta 
} from '@/utils/apiResponseHandler';

export function useApiRequest(options = {}) {
  const {
    showMessages = true,
    autoExtractData = true,
    retryAttempts = 0,
    retryDelay = 1000,
  } = options;

  // State
  const isLoading = ref(false);
  const data = ref(null);
  const error = ref(null);
  const lastResponse = ref(null);

  // Message handling (optional)
  const messageHandler = showMessages ? useMessageHandler() : null;

  /**
   * Execute an API request
   */
  const execute = async (apiCall, ...args) => {
    isLoading.value = true;
    error.value = null;
    
    if (messageHandler) {
      messageHandler.clearMessage();
    }

    let attempts = 0;
    const maxAttempts = retryAttempts + 1;

    while (attempts < maxAttempts) {
      try {
        const response = await apiCall(...args);
        lastResponse.value = response;
        
        // Extract data if auto-extraction is enabled
        if (autoExtractData) {
          data.value = extractResponseData(response);
        } else {
          data.value = response;
        }

        isLoading.value = false;
        return response;

      } catch (err) {
        attempts++;
        
        if (attempts >= maxAttempts) {
          // Final attempt failed
          error.value = err;
          isLoading.value = false;
          
          const errorInfo = extractErrorInfo(err);
          const errorMessage = getErrorMessage(err, 'An unexpected error occurred');
          
          logError(err, 'useApiRequest');
          
          if (messageHandler) {
            messageHandler.setErrorMessage(errorMessage);
          }
          
          throw err;
        } else {
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
      }
    }
  };

  /**
   * Reset state
   */
  const reset = () => {
    isLoading.value = false;
    data.value = null;
    error.value = null;
    lastResponse.value = null;
    
    if (messageHandler) {
      messageHandler.clearMessage();
    }
  };

  // Computed properties
  const hasData = computed(() => data.value !== null);
  const hasError = computed(() => error.value !== null);
  const isIdle = computed(() => !isLoading.value && !hasData.value && !hasError.value);

  return {
    // State
    isLoading,
    data,
    error,
    lastResponse,
    
    // Computed
    hasData,
    hasError,
    isIdle,
    
    // Methods
    execute,
    reset,
    
    // Message handler (if enabled)
    ...(messageHandler || {}),
  };
}

/**
 * Specialized composable for paginated API requests
 */
export function usePaginatedApiRequest(options = {}) {
  const {
    itemsPerPage = 10,
    dataExtractor = (data) => Array.isArray(data) ? data : data?.items || [],
    ...apiOptions
  } = options;

  const apiRequest = useApiRequest(apiOptions);
  
  // Pagination state
  const currentPage = ref(1);
  const totalItems = ref(0);
  const totalPages = ref(0);
  const items = ref([]);

  /**
   * Fetch paginated data
   */
  const fetchPage = async (apiCall, page = currentPage.value, ...args) => {
    const offset = (page - 1) * itemsPerPage;
    const params = {
      limit: itemsPerPage,
      offset: offset,
      ...args[0] // Merge with any additional params
    };

    try {
      const response = await apiRequest.execute(apiCall, params);
      const responseData = extractResponseData(response);
      const paginationMeta = extractPaginationMeta(response);

      items.value = responseData ? dataExtractor(responseData) : [];
      
      if (paginationMeta) {
        totalItems.value = paginationMeta.total;
        totalPages.value = Math.ceil(paginationMeta.total / itemsPerPage);
      }

      currentPage.value = page;
      return response;

    } catch (error) {
      items.value = [];
      throw error;
    }
  };

  /**
   * Go to specific page
   */
  const goToPage = async (apiCall, page, ...args) => {
    if (page >= 1 && page <= totalPages.value) {
      await fetchPage(apiCall, page, ...args);
    }
  };

  /**
   * Go to next page
   */
  const nextPage = async (apiCall, ...args) => {
    if (currentPage.value < totalPages.value) {
      await fetchPage(apiCall, currentPage.value + 1, ...args);
    }
  };

  /**
   * Go to previous page
   */
  const prevPage = async (apiCall, ...args) => {
    if (currentPage.value > 1) {
      await fetchPage(apiCall, currentPage.value - 1, ...args);
    }
  };

  /**
   * Reset pagination state
   */
  const resetPagination = () => {
    currentPage.value = 1;
    totalItems.value = 0;
    totalPages.value = 0;
    items.value = [];
    apiRequest.reset();
  };

  // Computed properties
  const hasNextPage = computed(() => currentPage.value < totalPages.value);
  const hasPrevPage = computed(() => currentPage.value > 1);
  const isEmpty = computed(() => items.value.length === 0);

  return {
    // Inherit from base API request
    ...apiRequest,
    
    // Pagination state
    currentPage,
    totalItems,
    totalPages,
    items,
    
    // Computed
    hasNextPage,
    hasPrevPage,
    isEmpty,
    
    // Methods
    fetchPage,
    goToPage,
    nextPage,
    prevPage,
    resetPagination,
  };
}

/**
 * Specialized composable for search with pagination
 */
export function useSearchableApiRequest(options = {}) {
  const {
    searchDelay = 300,
    minSearchLength = 2,
    ...paginatedOptions
  } = options;

  const paginatedRequest = usePaginatedApiRequest(paginatedOptions);
  
  // Search state
  const searchQuery = ref('');
  const isSearching = ref(false);
  const searchResults = ref([]);
  let searchTimeout = null;

  /**
   * Perform search
   */
  const search = async (apiCall, query = searchQuery.value, ...args) => {
    if (!query || query.length < minSearchLength) {
      searchResults.value = [];
      isSearching.value = false;
      return;
    }

    isSearching.value = true;
    
    try {
      const params = { search: query, ...args[0] };
      const response = await paginatedRequest.execute(apiCall, params);
      searchResults.value = paginatedRequest.items.value;
      return response;
    } catch (error) {
      searchResults.value = [];
      throw error;
    } finally {
      isSearching.value = false;
    }
  };

  /**
   * Debounced search
   */
  const debouncedSearch = (apiCall, query, ...args) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    
    searchTimeout = setTimeout(() => {
      search(apiCall, query, ...args);
    }, searchDelay);
  };

  /**
   * Clear search
   */
  const clearSearch = () => {
    searchQuery.value = '';
    searchResults.value = [];
    isSearching.value = false;
    
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
  };

  return {
    // Inherit from paginated request
    ...paginatedRequest,
    
    // Search state
    searchQuery,
    isSearching,
    searchResults,
    
    // Methods
    search,
    debouncedSearch,
    clearSearch,
  };
}
