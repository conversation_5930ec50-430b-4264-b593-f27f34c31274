import axios from "axios";
import { logApiRequest, logApiResponse, logApiError } from "@/utils/logger";
import { hasAdminAccess } from "@/utils/authHelpers";

// Get API base URL from environment variables
// Priority: VITE_API_BASE_URL > relative paths for proxy
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || "/api";

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

// Request interceptor - can be used for adding auth tokens
apiClient.interceptors.request.use(
  (config) => {
    // Log API request
    logApiRequest(
      config.method?.toUpperCase() || "GET",
      config.url,
      config.data,
    );

    // Check if authentication is enabled from environment variables
    const authEnabled = import.meta.env.VITE_AUTH_ENABLED === "true";

    // Add session-based authentication if needed
    if (authEnabled) {
      // Get token storage key from env or use default
      const tokenKey = import.meta.env.VITE_AUTH_TOKEN_KEY || "auth_token";
      const token = localStorage.getItem(tokenKey);

      // Get token prefix from env or use default Bearer
      const tokenPrefix = import.meta.env.VITE_AUTH_TOKEN_PREFIX || "Bearer";

      if (token) {
        config.headers.Authorization = `${tokenPrefix} ${token}`;
      }

      // Add credentials to allow cookies for session-based auth
      // Can be disabled via environment variable
      const withCredentials =
        import.meta.env.VITE_AUTH_WITH_CREDENTIALS !== "false";
      if (withCredentials) {
        config.withCredentials = true;
      }
    }

    return config;
  },
  (error) => {
    logApiError("REQUEST", "interceptor", error);
    return Promise.reject(error);
  },
);

// Response interceptor - for global error handling
apiClient.interceptors.response.use(
  (response) => {
    // Log successful API response
    logApiResponse(
      response.config?.method?.toUpperCase() || "GET",
      response.config?.url || "unknown",
      response.status,
      response.data,
    );
    return response;
  },
  (error) => {
    // Log API error
    logApiError(
      error.config?.method?.toUpperCase() || "GET",
      error.config?.url || "unknown",
      error,
    );

    // Handle common errors globally
    if (error.response) {
      // Server responded with an error status

      // Check if authentication is enabled from environment variables
      const authEnabled = import.meta.env.VITE_AUTH_ENABLED === "true";

      // Handle authentication errors
      if (authEnabled && error.response.status === 401) {
        // Unauthorized - redirect to login
        const loginUrl = import.meta.env.VITE_AUTH_LOGIN_URL || "/auth/login";
        window.location.href = loginUrl;
      }

      // Handle forbidden errors
      if (authEnabled && error.response.status === 403) {
        // Forbidden - redirect to access denied page if configured
        const accessDeniedUrl = import.meta.env.VITE_AUTH_ACCESS_DENIED_URL;
        if (accessDeniedUrl) {
          window.location.href = accessDeniedUrl;
        }
      }
    } else if (error.request) {
      // Request was made but no response received
      logApiError("REQUEST", "no-response", error);
    } else {
      // Something else happened while setting up the request
      logApiError("REQUEST", "setup-error", error);
    }

    return Promise.reject(error);
  },
);

/**
 * Centralized API request handler
 * @param {string} method - HTTP method (get, post, put, delete)
 * @param {string} url - API endpoint
 * @param {Object} data - Request payload (for POST, PUT)
 * @param {Object} params - URL parameters (for GET)
 * @param {Object} options - Additional axios options
 * @returns {Promise} - Axios promise
 */
const apiRequest = (method, url, data = null, params = null, options = {}) => {
  const config = {
    method,
    url,
    ...options,
  };

  if (data) {
    config.data = data;
  }

  if (params) {
    config.params = params;
  }

  return apiClient(config);
};

// Export the axios instance
export default apiClient;

// Export the centralized request handler
export const request = {
  get: (url, params = null, options = {}) =>
    apiRequest("get", url, null, params, options),
  post: (url, data = null, options = {}) =>
    apiRequest("post", url, data, null, options),
  put: (url, data = null, options = {}) =>
    apiRequest("put", url, data, null, options),
  delete: (url, data = null, options = {}) =>
    apiRequest("delete", url, data, null, options),
  patch: (url, data = null, options = {}) =>
    apiRequest("patch", url, data, null, options),
};

// Auth state management
let cachedUserInfo = null;
let userInfoPromise = null;
let lastFetchTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache

// Function to clear cached user info
const clearUserInfoCache = () => {
  cachedUserInfo = null;
  userInfoPromise = null;
  lastFetchTime = 0;
  // Dispatch event to notify components that auth state may have changed
  window.dispatchEvent(new CustomEvent('auth-state-changed'));
};

// Function to get user info with caching
const getCachedUserInfo = async (forceRefresh = false) => {
  const now = Date.now();

  // If we have cached data and it's still valid, return it
  if (!forceRefresh && cachedUserInfo && (now - lastFetchTime) < CACHE_DURATION) {
    return cachedUserInfo;
  }

  // If there's already a request in progress, wait for it
  if (userInfoPromise) {
    return userInfoPromise;
  }

  // Make a new request
  userInfoPromise = (async () => {
    try {
      const response = await fetch('/auth/userinfo', {
        method: 'GET',
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        cachedUserInfo = data;
        lastFetchTime = now;
        return data;
      } else {
        cachedUserInfo = { authenticated: false, user: null };
        lastFetchTime = now;
        return cachedUserInfo;
      }
    } catch (error) {
      cachedUserInfo = { authenticated: false, user: null };
      lastFetchTime = now;
      return cachedUserInfo;
    } finally {
      userInfoPromise = null;
    }
  })();

  return userInfoPromise;
};

// Export common API functions
export const api = {
  // Auth caching functions at root level
  getCachedUserInfo,
  clearUserInfoCache,

  // Auth endpoints
  auth: {
    login: () => {
      const loginUrl = import.meta.env.VITE_AUTH_LOGIN_URL || "/auth/login";
      window.location.href = loginUrl;
    },
    logout: () => {
      const logoutUrl = import.meta.env.VITE_AUTH_LOGOUT_URL || "/auth/logout";
      window.location.href = logoutUrl;
    },
    getUserInfo: () => {
      const userInfoEndpoint =
        import.meta.env.VITE_AUTH_USERINFO_URL || "/auth/userinfo";
      return request.get(userInfoEndpoint);
    },
    getCurrentUserId: () => {
      // Get user info from localStorage
      const userInfoStr = localStorage.getItem("user_info");
      if (!userInfoStr) return null;

      try {
        const userInfo = JSON.parse(userInfoStr);
        return userInfo.id || userInfo.sub || null;
      } catch (error) {
        logApiError("GET", "parse-user-info", error);
        return null;
      }
    },
    hasAdminAccess: () => {
      // Check if current user has admin access
      const userInfoStr = localStorage.getItem("user_info");
      if (!userInfoStr) return false;

      try {
        const userInfo = JSON.parse(userInfoStr);
        return hasAdminAccess(userInfo);
      } catch (error) {
        logApiError("GET", "check-admin-access", error);
        return false;
      }
    },
  },

  // Admin endpoints
  admin: {
    // Assessments
    getAssessments: (params = {}) => request.get("/admin/assessments", params),
    getAssessment: (id) => request.get(`/admin/assessment/${id}`),
    createAssessment: (data) => request.post("/admin/quiz", data),
    getAssessmentQuestions: (id) =>
      request.get(`/admin/assessment-questions/${id}`),
    addFinalQuestions: (data) => request.post("/admin/final-questions", data),

    // Skills
    getSkills: (params = {}) => request.get("/admin/skills", params),
    getSkill: (id) => request.get(`/admin/skills/${id}`),
    createSkill: (data) => request.post("/admin/skills", data),
    suggestSkillDescription: (data) =>
      request.post("/admin/suggest-skill-description", data),
    getSkillQuestions: (skillId) =>
      request.get(`/admin/skill-questions/${skillId}`),
    generateSkillQuestions: (data) =>
      request.post("/generate-questions", data, {
        timeout: 320000,
      }),
    getSkillQuestionCounts: () => request.get("/admin/skill-question-counts"),

    // Sessions
    getSessions: (params = {}) => request.get("/admin/sessions", params),
    getSessionDetails: (sessionId) =>
      request.get(`/admin/sessions/${sessionId}/details`),
    createSession: (data) => request.post("/admin/sessions", data),
    getSessionUser: (code) => request.get(`/admin/sessions/${code}/user`), // code must be a 6-digit session code
    getAssessmentsWithSessions: () =>
      request.get("/admin/assessments-with-sessions"),
    generateLink: (data) => request.post("/admin/generate-link", data),

    // Reports
    generateReport: (data) => request.post("/admin/reports", data),
    getUserWiseReport: (params) =>
      request.get("/admin/reports/user-wise", params),
    getSkillwiseHeatmap: () => request.get("/admin/reports/skillwise-heatmap"),
    getAssessmentWiseReport: (params) =>
      request.get("/admin/reports/assessment-wise", params),

    // Users
    getUsers: () => request.get("/admin/users"),
    getUserAssessments: (userId) =>
      request.get(`/admin/users/${userId}/assessments`),
    getUserSkillPerformance: (userId) =>
      request.get(`/admin/users/${userId}/skill-performance`),
  },

  user: {
    getUserAssessmentsByEmail: (email) =>
      request.get(`/user/${email}/assessments`),
    getUserSkillPerformanceByEmail: (email) =>
      request.get(`/user/${email}/skill-performance`),
    getUserSessions: (email) =>
      request.get(`/user/${email}/sessions`, null, {
        headers: { "X-Debug": "true" },
      }),
  },

  // Quiz endpoints
  quiz: {
    validateSessionCode: (data) => request.post("/validate_session_code", data),
    startSession: (data) => request.post("/start_session", data),
    getQuestions: (sessionCode, params) =>
      request.get(`/get_questions/${sessionCode}`, params),
    checkAndSaveAnswer: (data) => request.post("/check_and_save_answer", data),
    submitSession: (data) => request.post("/submit_session", data),
    pauseSession: (data) => request.post("/pause_session", data),
    resumeSession: (data) => request.post("/resume_session", data),
    getPauseStatus: (sessionCode) =>
      request.get(`/pause_status/${sessionCode}`),
  },
};
