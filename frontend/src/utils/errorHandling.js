/**
 * Utility functions for handling API errors
 */
import { error as logErrorUtil } from './logger';

/**
 * Extracts a detailed error message from an Axios error response.
 * @param {object} response - The error.response object from Axios.
 * @returns {string|null} The extracted error message or null if not found.
 */
const getErrorFromResponse = (response) => {
  if (!response?.data) return null;
  return response.data.detail || response.data.message || response.data.error;
};

/**
 * Provides a user-friendly message for common HTTP status codes.
 * @param {number} status - The HTTP status code.
 * @returns {string|null} A friendly error message or null.
 */
const getMessageForStatusCode = (status) => {
  switch (status) {
    case 400:
      return "Invalid request. Please check your input.";
    case 401:
      return "You need to be logged in to perform this action.";
    case 403:
      return "You don't have permission to perform this action.";
    case 404:
      return "The requested resource was not found.";
    case 500:
      return "Server error. Please try again later.";
    default:
      return null;
  }
};

/**
 * Extract a user-friendly error message from an API error.
 * @param {Error} error - The error object from Axios.
 * @param {string} defaultMessage - Default message to show if no specific error is found.
 * @returns {string} A user-friendly error message.
 */
export const getErrorMessage = (
  error,
  defaultMessage = "An error occurred. Please try again.",
) => {
  if (!error) return defaultMessage;

  if (error.response) {
    const detail = getErrorFromResponse(error.response);
    if (detail) return detail;

    const statusMessage = getMessageForStatusCode(error.response.status);
    if (statusMessage) return statusMessage;

    return `Error ${error.response.status}: An unexpected error occurred.`;
  }

  if (error.request) {
    return "Network error. Please check your connection and try again.";
  }

  return error.message || defaultMessage;
};

/**
 * Log an error with additional context using the centralized logger
 * @param {Error} error - The error object
 * @param {string} context - Context information about where the error occurred
 */
export const logError = (error, context = "") => {
  const message = context ? `Error in ${context}` : "An error occurred";
  logErrorUtil(message, { error, context });
};
