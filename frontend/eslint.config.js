import eslintPluginVue from "eslint-plugin-vue";

export default [
  // Apply recommended Vue rules to .vue files
  ...eslintPluginVue.configs["flat/recommended"],
  {
    files: ["**/*.js", "**/*.vue"],
    languageOptions: {
      ecmaVersion: 2020,
      sourceType: "module",
    },
    rules: {
      // Custom rules
      "max-len": ["warn", { code: 120 }],
      // You can override Vue rules here if needed
      "vue/max-len": ["warn", { code: 120 }],
      // Allow single-word component names for common layout components and UI components
      "vue/multi-word-component-names": ["error", {
        "ignores": [
          "Header", "Footer", "Hero", "Layout", "Login", "Sessions", "Link", "Home", "Callback",
          "Alert", "Button", "Calendar", "Card", "Dialog", "Input", "Label", "Pagination",
          "Select", "Tabs", "Toggle"
        ]
      }],
      // Allow v-html but warn about potential XSS
      "vue/no-v-html": "warn",
    },
  },
  // Completely ignore UI components folder
  {
    ignores: ["src/components/ui/**/*"]
  },
];
