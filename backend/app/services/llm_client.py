"""
Handles API interactions with the Ollama model, including querying models
and retrieving available model lists.

Classes:
- OllamaClient: Manages API requests for querying models and retrieving models list.

Functions:
- get_models: Fetches available models from Ollama API.
- query_model: Sends a prompt to a selected model for generating commands.
"""

import asyncio
import json
import os
import time
import uuid

import aiohttp
from aiohttp import ContentTypeError
from dotenv import load_dotenv

from ..utils.logger import debug, error, info

# Load environment variables from .env file
load_dotenv()


BASE_URL = os.getenv("BASE_URL")
API_KEY = os.getenv("API_KEY")


async def get_models():
    """
    Retrieves available models from the Ollama API.

    Returns:
        dict: API response containing model data or an error message.
    """
    request_id = str(uuid.uuid4())[:8]
    url = f"{BASE_URL}/api/models"
    headers = {"Authorization": f"Bearer {API_KEY}"}

    info(f"[{request_id}] Starting request to get models from {url}")

    start_time = time.time()
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(url, headers=headers) as response:
                elapsed = time.time() - start_time
                info(
                    f"[{request_id}] Response received in {elapsed:.2f}s with status {response.status}"
                )

                if response.status == 200:
                    result = await response.json()
                    debug(
                        f"[{request_id}] Successful response: {json.dumps(result)[:200]}..."
                    )
                    return result

                error_text = await response.text()
                error(
                    f"[{request_id}] Error response ({response.status}): {error_text}"
                )
                return {"error": response.status, "message": error_text}

        except aiohttp.ClientError as err:
            elapsed = time.time() - start_time
            error(f"[{request_id}] Client error after {elapsed:.2f}s: {str(err)}")
            return {"error": "ClientError", "message": str(err)}


async def query_model(prompt, model_id, timeout=300):
    """
    Queries a specified model with a prompt.

    Args:
        prompt (str): Input prompt for the model.
        model_id (str): ID of the model to query.
        timeout (int): Timeout in seconds for the request.

    Returns:
        dict: API response with query data or an error message.
    """
    request_id = str(uuid.uuid4())[:8]
    url = f"{BASE_URL}/api/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}",
    }
    data = {"model": model_id, "messages": [{"role": "user", "content": prompt}]}

    # Configure timeout
    timeout_obj = aiohttp.ClientTimeout(total=timeout)

    # Log request details
    prompt_preview = prompt[:50] + "..." if len(prompt) > 50 else prompt
    info(f"[{request_id}] Starting request to {url} for model {model_id}")
    debug(f"[{request_id}] Prompt preview: {prompt_preview}")
    debug(f"[{request_id}] Request timeout: {timeout}s")

    start_time = time.time()
    tcp_conn_start = None

    async with aiohttp.ClientSession(timeout=timeout_obj) as session:
        try:
            # Add connection timing trace
            tcp_conn_start = time.time()
            debug(f"[{request_id}] Establishing TCP connection...")

            # Add request tracing
            trace_config = aiohttp.TraceConfig()

            async def on_request_start(session, trace_config_ctx, params):
                debug(f"[{request_id}] Request started")

            async def on_request_end(session, trace_config_ctx, params):
                debug(f"[{request_id}] Request ended")

            async def on_connection_create_start(session, trace_config_ctx, params):
                debug(f"[{request_id}] Connection create started")

            async def on_connection_create_end(session, trace_config_ctx, params):
                debug(f"[{request_id}] Connection create ended")

            async def on_dns_resolvehost_start(session, trace_config_ctx, params):
                debug(f"[{request_id}] DNS resolution started for {params.host}")

            async def on_dns_resolvehost_end(session, trace_config_ctx, params):
                debug(f"[{request_id}] DNS resolution ended for {params.host}")

            trace_config.on_request_start.append(on_request_start)
            trace_config.on_request_end.append(on_request_end)
            trace_config.on_connection_create_start.append(on_connection_create_start)
            trace_config.on_connection_create_end.append(on_connection_create_end)
            trace_config.on_dns_resolvehost_start.append(on_dns_resolvehost_start)
            trace_config.on_dns_resolvehost_end.append(on_dns_resolvehost_end)

            trace_config.freeze()
            session._trace_configs.append(trace_config)

            # Make the request with detailed timing
            request_start = time.time()
            if tcp_conn_start:
                debug(
                    f"[{request_id}] TCP connection established in {request_start - tcp_conn_start:.2f}s"
                )

            async with session.post(url, headers=headers, json=data) as response:
                response_time = time.time()
                elapsed = response_time - start_time
                info(
                    f"[{request_id}] Response received in {elapsed:.2f}s with status {response.status}"
                )
                debug(f"[{request_id}] Response headers: {dict(response.headers)}")

                if response.status == 200:
                    response_json = await response.json()
                    parsing_time = time.time() - response_time
                    debug(f"[{request_id}] Response parsed in {parsing_time:.2f}s")
                    info(
                        f"[{request_id}] Successfully received response from model {model_id}"
                    )

                    # Log a preview of the response
                    response_preview = (
                        str(response_json)[:200] + "..."
                        if len(str(response_json)) > 200
                        else str(response_json)
                    )
                    debug(f"[{request_id}] Response preview: {response_preview}")

                    return response_json

                # Handle different error status codes
                error_text = await response.text()
                error(
                    f"[{request_id}] Error response ({response.status}): {error_text}"
                )

                try:
                    error_json = await response.json()
                    error_message = error_json.get("error", error_text)
                    debug(f"[{request_id}] Parsed error JSON: {error_json}")
                except (ContentTypeError, json.JSONDecodeError) as e:
                    error_message = error_text
                    debug(f"[{request_id}] Could not parse error as JSON: {e}")

                return {
                    "error": response.status,
                    "message": error_message,
                    "status": "error",
                }
        except asyncio.TimeoutError:
            elapsed = time.time() - start_time
            error(
                f"[{request_id}] Request timed out after {elapsed:.2f}s (timeout set to {timeout}s)"
            )
            return {
                "error": "Timeout",
                "message": f"Request timed out after {timeout} seconds",
                "status": "error",
            }
        except aiohttp.ClientError as err:
            elapsed = time.time() - start_time
            error(f"[{request_id}] Client error after {elapsed:.2f}s: {str(err)}")
            return {"error": "ClientError", "message": str(err), "status": "error"}
        except Exception as e:
            elapsed = time.time() - start_time
            error(
                f"[{request_id}] Unexpected error after {elapsed:.2f}s: {str(e)}",
                exception=e,
            )
            return {"error": "UnexpectedError", "message": str(e), "status": "error"}
