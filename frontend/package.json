{"name": "herbit-frontend", "type": "module", "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "serve": "vite preview"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/node": "^22.14.1", "@vitejs/plugin-vue": "^5.2.3", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-vue": "^9.31.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^6.2.4"}, "dependencies": {"@floating-ui/vue": "^1.1.6", "@fontsource/fira-code": "^5.2.5", "@fontsource/open-sans": "^5.2.5", "@unovis/ts": "^1.5.2", "@unovis/vue": "^1.5.2", "@vueuse/core": "^13.3.0", "animate.css": "^4.1.1", "apexcharts": "^4.7.0", "axios": "^1.8.4", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "font-awesome": "^4.7.0", "hashids": "^2.3.0", "lucide-vue-next": "^0.503.0", "marked": "^15.0.12", "postcss-import": "^16.1.0", "reka-ui": "^2.3.0", "shadcn-vue": "^1.0.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.8", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-demi": "^0.14.10", "vue-router": "^4.5.0", "vue3-apexcharts": "^1.8.0", "vuex": "^4.0.2"}}