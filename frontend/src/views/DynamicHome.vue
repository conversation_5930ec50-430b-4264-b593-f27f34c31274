<template>
  <div>
    <!-- Show admin home if user has admin group -->
    <Home v-if="showAdminHome" />

    <!-- Show user home if user has employee group -->
    <UserHome v-if="showUserHome" />

    <!-- Show message if user has no relevant groups -->
    <div
      v-if="!showAdminHome && !showUserHome"
      class="min-h-screen flex items-center justify-center"
    >
      <div class="text-center p-8 bg-gray-100 rounded-lg shadow-md">
        <h1 class="text-2xl font-bold text-gray-800 mb-4">
          Access Restricted
        </h1>
        <p class="text-gray-600">
          You don't have permission to access this application.
        </p>
        <p class="text-gray-600 mt-2">
          Please contact your administrator for assistance.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { debug, info, warning, error } from "@/utils/logger";
import { shouldShowAdminHome, shouldShowUserHome, getUserPrimaryRole } from "@/utils/authHelpers";
import Home from "./Home.vue";
import UserHome from "./UserHome.vue";

// Track which components to show
const showAdminHome = ref(false);
const showUserHome = ref(false);

onMounted(() => {
  // Determine which components to show based on user access
  const userInfoStr = localStorage.getItem("user_info");
  if (userInfoStr) {
    try {
      const userInfo = JSON.parse(userInfoStr);
      debug("User info in DynamicHome", { userInfo });

      // Use the new auth helper functions
      const shouldShowAdmin = shouldShowAdminHome(userInfo);
      const shouldShowUser = shouldShowUserHome(userInfo);
      const primaryRole = getUserPrimaryRole(userInfo);

      debug("User access analysis", {
        shouldShowAdmin,
        shouldShowUser,
        primaryRole,
        userId: userInfo.sub,
        userEmail: userInfo.email,
        groups: userInfo.groups,
      });

      // Set component visibility
      showAdminHome.value = shouldShowAdmin;
      showUserHome.value = shouldShowUser;

      // Log the decision
      if (shouldShowAdmin) {
        info("User has admin access, showing admin home", {
          primaryRole,
          userId: userInfo.sub,
          userEmail: userInfo.email,
        });
      } else if (shouldShowUser) {
        info("User has employee access, showing user home", {
          primaryRole,
          userId: userInfo.sub,
          userEmail: userInfo.email,
        });
      } else {
        warning("User has no relevant access, showing access restricted message", {
          primaryRole,
          userId: userInfo.sub,
          userEmail: userInfo.email,
          groups: userInfo.groups,
        });
      }

      // Log final component visibility state
      debug("Final component visibility state", {
        showAdminHome: showAdminHome.value,
        showUserHome: showUserHome.value,
      });
    } catch (e) {
      error("Error parsing user info", { error: e });
    }
  } else {
    warning("No user info found in localStorage");
  }
});
</script>
