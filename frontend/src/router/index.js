import { createRouter, createWebHistory } from "vue-router";
import { debug, info, warning, error, logNavigation } from "@/utils/logger";
import { hasAdminAccess } from "@/utils/authHelpers";
import { api } from "@/services/api";
import DynamicHome from "@/views/DynamicHome.vue";

const routes = [
  {
    path: "/",
    name: "Home",
    component: DynamicHome,
    meta: { requiresAuth: true },
  },

  // Auth routes
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/Login.vue"),
    meta: { requiresAuth: false },
  },
  {
    path: "/callback",
    name: "Callback",
    component: () => import("@/views/Callback.vue"),
    meta: { requiresAuth: false },
  },

  // Admin routes
  { path: '/create-assessment', name: 'CreateAssessment', component: () => import('@/views/AssessmentCreate.vue'), meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/list-assessments', name: 'ListAssessments', component: () => import('@/views/AssessmentList.vue'), meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/assessment/:id', name: 'AssessmentDetail', component: () => import('@/views/AssessmentDetails.vue'), meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/sessions', name: 'SessionsList', component: () => import('@/views/SessionsList.vue'), meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/session/:sessionId', name: 'SessionDetail', component: () => import('@/views/SessionsDetails.vue'), meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/generate-sessions', name: 'GenerateSessions', component: () => import('@/views/Sessions.vue'), meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/create-skill', name: 'CreateSkill', component: () => import('@/views/SkillsCreate.vue'), meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/list-skills', name: 'ListSkills', component: () => import('@/views/SkillsList.vue'), meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/skill/:id', name: 'SkillDetail', component: () => import('@/views/SkillsDetails.vue'), meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/report-generate', name: 'ReportGenerate', component: () => import('@/views/ReportGenerate.vue'), meta: { requiresAuth: true, requiresAdmin: true } },

  // User routes
  {
    path: "/user-home",
    name: "UserHome",
    component: () => import("@/views/UserHome.vue"),
    meta: { requiresAuth: true },
  },
  {
    path: "/report",
    name: "UserReports",
    component: () => import("@/views/UserReport.vue"),
    meta: { requiresAuth: true },
  },
  {
    path: "/user-sessions",
    name: "UserSessions",
    component: () => import("@/views/UserSessions.vue"),
    meta: { requiresAuth: true },
  },

  // Quiz taking routes - public access for users to take quizzes
  {
    path: "/quiz/:sessionCode",
    name: "QuizWithSession",
    component: () => import("@/views/TakeQuiz.vue"),
    meta: { requiresAuth: true },
  },

  { path: "/:pathMatch(.*)*", redirect: "/" },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// Add global navigation guards for error handling
router.onError((routerError) => {
  error("Router error:", {
    error: routerError.message,
    stack: routerError.stack,
  });
});

// Add before each guard to handle authentication and navigation issues
router.beforeEach(async (to, _from, next) => {
  try {
    // Log navigation
    logNavigation(_from.path || "unknown", to.path);

    // Check if the route exists
    if (!to.matched.length) {
      warning("Route not found:", { path: to.path });
      next("/login");
      return;
    }

    // Check if user is authenticated for protected routes using cached auth
    let isAuthenticated = false;

    try {
      // Use cached userinfo to avoid repeated network calls
      const data = await api.getCachedUserInfo(false); // Use cache for route guards
      isAuthenticated = data.authenticated;
    } catch (error) {
      // If there's an error checking auth status, fallback to localStorage
      debug('Error checking cached auth status, falling back to localStorage:', { error });
      const userInfoStr = localStorage.getItem('user_info');
      if (userInfoStr) {
        try {
          const userInfo = JSON.parse(userInfoStr);
          isAuthenticated = userInfo && (userInfo.sub || userInfo.email);
        } catch (e) {
          debug('Error parsing user info from localStorage:', { error: e });
          isAuthenticated = false;
        }
      } else {
        isAuthenticated = false;
      }
    }

    debug('Authentication check result:', {
      route: to.path,
      requiresAuth: to.meta.requiresAuth,
      isAuthenticated,
      userInfoInLocalStorage: !!localStorage.getItem('user_info')
    });

    if (to.meta.requiresAuth && !isAuthenticated) {
      // If route requires authentication and user is not authenticated
      info("Authentication required, redirecting to login");
      next("/login");
      return;
    }

    // If user is authenticated and trying to access login page, redirect to home
    if (to.path === "/login" && isAuthenticated) {
      info("User is already authenticated, redirecting to home");
      next("/");
      return;
    }

    // Check admin-only routes
    if (to.meta.requiresAdmin && isAuthenticated) {
      const userInfoStr = localStorage.getItem("user_info");
      if (userInfoStr) {
        try {
          const userInfo = JSON.parse(userInfoStr);
          const userHasAdminAccess = hasAdminAccess(userInfo);

          debug("User admin access analysis for admin route:", {
            groups: userInfo.groups,
            hasAdminAccess: userHasAdminAccess,
            userId: userInfo.sub,
            userEmail: userInfo.email,
          });

          // If user doesn't have admin access, redirect to home
          if (!userHasAdminAccess) {
            warning(
              "User without admin access trying to access admin route, redirecting to home",
            );
            next("/");
            return;
          }
        } catch (e) {
          error("Error parsing user info:", { error: e });
          next("/");
          return;
        }
      } else {
        // No user info, redirect to home
        warning("No user info found for admin route access, redirecting to home");
        next("/");
        return;
      }
    }

    next();
  } catch (navError) {
    error("Navigation error:", {
      error: navError.message,
      stack: navError.stack,
    });
    next("/login");
  }
});

export default router;
