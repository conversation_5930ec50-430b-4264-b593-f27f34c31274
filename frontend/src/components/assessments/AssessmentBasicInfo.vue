<template>
  <section class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8">
    <h2 class="text-xl font-semibold text-white mb-6 bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent">
      Basic Information
    </h2>

    <div class="space-y-6">
      <!-- Assessment Name -->
      <div>
        <label for="assessmentName" class="block text-white font-medium mb-2">
          Assessment Name
        </label>
        <input
          id="assessmentName"
          v-model="formData.name"
          name="assessmentName"
          type="text"
          autocomplete="off"
          placeholder="e.g. DevOps Basics"
          required
          class="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2.5 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50"
          :class="{ 'border-red-500': errors.name }"
        >
        <div v-if="errors.name" class="mt-1 text-xs text-red-400">
          {{ errors.name }}
        </div>
      </div>

      <!-- Assessment Duration -->
      <div>
        <label for="assessmentDuration" class="block text-white font-medium mb-2">
          Assessment Duration (minutes)
        </label>
        <input
          id="assessmentDuration"
          v-model.number="formData.duration"
          type="number"
          name="assessmentDuration"
          min="5"
          max="180"
          autocomplete="off"
          class="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2.5 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50"
          :class="{ 'border-red-500': errors.duration }"
          required
        >
        <div class="mt-1 text-xs text-white/60">
          Set the time limit for completing this assessment
        </div>
        <div v-if="errors.duration" class="mt-1 text-xs text-red-400">
          {{ errors.duration }}
        </div>
      </div>

      <!-- Assessment Description -->
      <div>
        <label for="assessmentDescription" class="block text-white font-medium mb-2">
          Assessment Description
        </label>
        <textarea
          id="assessmentDescription"
          v-model="formData.description"
          name="assessmentDescription"
          autocomplete="off"
          placeholder="e.g. A comprehensive assessment of DevOps fundamentals including CI/CD pipelines, containerization, and infrastructure automation"
          required
          class="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2.5 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50 resize-y min-h-[100px]"
          :class="{ 'border-red-500': errors.description }"
        />
        <div class="mt-1 text-xs text-white/60">
          Provide a detailed description of what this assessment covers (minimum 20 characters)
        </div>
        <div v-if="errors.description" class="mt-1 text-xs text-red-400">
          {{ errors.description }}
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
/**
 * Assessment Basic Information Component
 * Handles the basic form fields for assessment creation
 */

const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  errors: {
    type: Object,
    default: () => ({})
  }
});

// Emit events for parent to handle
const emit = defineEmits(['update:formData']);

// Watch for changes and emit to parent
import { watch } from 'vue';

watch(() => props.formData, (newData) => {
  emit('update:formData', newData);
}, { deep: true });
</script>