<template>
  <section class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8">
    <h2 class="text-xl font-semibold text-white mb-6 bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent">
      Skills Selection
    </h2>

    <div>
      <label class="block text-white font-medium mb-2">Select Skills</label>
      <div class="mb-2 text-xs text-white/60">
        You can select multiple skills for this assessment
      </div>

      <div class="flex items-center space-x-2">
        <Select
          :model-value="selectedSkillIds"
          :disabled="skillsLoading"
          multiple
          class="w-full"
          @update:model-value="handleSkillSelectionChange"
        >
          <SelectTrigger class="w-full bg-white/5 border border-white/10 text-white focus:ring-phantom-blue/50 focus:border-phantom-blue/50 hover:bg-white/10">
            <SelectValue placeholder="Select skills..." />
          </SelectTrigger>
          <SelectContent class="bg-gray-900/95 backdrop-blur-sm border border-white/10 text-white">
            <div v-if="skills.length > 0" class="py-1.5 px-3 text-sm text-white/60">
              {{ skills.length }} skills available
            </div>

            <!-- No skills message -->
            <div v-if="skills.length === 0" class="py-4 px-3 text-sm text-white/60 text-center">
              No skills available. Please check the console for errors.
            </div>

            <!-- Skills list -->
            <template v-else>
              <SelectItem
                v-for="skill in skills"
                :key="skill.id"
                :value="skill.id"
              >
                {{ skill.name || "Unnamed Skill" }}
              </SelectItem>
            </template>
          </SelectContent>
        </Select>
        <Button
          variant="outline"
          size="sm"
          :disabled="skillsLoading || selectedSkillIds.length === 0"
          class="whitespace-nowrap border-phantom-blue/30 text-white hover:bg-phantom-blue/20"
          @click="clearSelection"
        >
          Clear
        </Button>
      </div>

      <!-- Selected Skills Display -->
      <div v-if="validSelectedSkillIds.length > 0" class="mt-4 flex flex-wrap gap-2">
        <div
          v-for="skillId in validSelectedSkillIds"
          :key="skillId"
          class="inline-flex items-center bg-phantom-blue/20 text-phantom-blue border border-phantom-blue/30 text-xs px-3 py-1.5 rounded-full"
        >
          {{ getSkillName(skillId) }}
          <button
            type="button"
            class="ml-2 text-phantom-blue hover:text-white hover:bg-phantom-blue/30 rounded-full p-0.5 transition-colors"
            @click="removeSkill(skillId)"
          >
            <SvgIcon name="x" size="xs" />
          </button>
        </div>
      </div>
      
      <!-- Validation Error -->
      <div v-if="hasInteractedWithSkills && selectedSkillIds.length === 0" class="mt-2 text-xs text-red-400">
        Please select at least one skill
      </div>
      <div v-if="errors.skills" class="mt-2 text-xs text-red-400">
        {{ errors.skills }}
      </div>
    </div>
  </section>
</template>

<script setup>
/**
 * Assessment Skills Selection Component
 * Handles skill selection for assessment creation
 */
import { computed } from 'vue';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import SvgIcon from '@/components/SvgIcon.vue';

const props = defineProps({
  selectedSkillIds: {
    type: Array,
    default: () => []
  },
  skills: {
    type: Array,
    default: () => []
  },
  skillsLoading: {
    type: Boolean,
    default: false
  },
  hasInteractedWithSkills: {
    type: Boolean,
    default: false
  },
  errors: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:selectedSkillIds', 'skill-interaction']);

// Computed properties
const validSelectedSkillIds = computed(() => {
  return props.selectedSkillIds.filter(
    (id) => id !== null && id !== undefined && id !== ""
  );
});

// Methods
const getSkillName = (skillId) => {
  if (!Array.isArray(props.skills) || !skillId) {
    return `Skill ${skillId}`;
  }

  const skill = props.skills.find(s => s.id === skillId || s.numericId === skillId);
  return skill?.name || `Skill ${skillId}`;
};

const handleSkillSelectionChange = (newSelection) => {
  emit('update:selectedSkillIds', newSelection);
  emit('skill-interaction');
};

const removeSkill = (skillId) => {
  const updatedSkills = props.selectedSkillIds.filter(id => id !== skillId);
  emit('update:selectedSkillIds', updatedSkills);
  emit('skill-interaction');
};

const clearSelection = () => {
  emit('update:selectedSkillIds', []);
  emit('skill-interaction');
};
</script>