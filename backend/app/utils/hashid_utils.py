"""
HashID utility module for encoding/decoding IDs with dynamic salts.
Provides functionality to hash assessment IDs, skill IDs, and session codes.
"""

import hashlib
from typing import Optional

from hashids import Hashids

from .logger import debug, warning


class DynamicHashID:
    """
    Dynamic HashID encoder/decoder with per-request salt generation.
    """

    def __init__(self, base_salt: str = "herbit_quiz_system", min_length: int = 8):
        """
        Initialize the DynamicHashID instance.

        Args:
            base_salt: Base salt string for hash generation
            min_length: Minimum length of generated hash
        """
        self.base_salt = base_salt
        self.min_length = min_length

    def _generate_dynamic_salt(self) -> str:
        """
        Generate a static salt for consistent encoding/decoding.

        Returns:
            Static salt string
        """
        # Use a static salt for consistent hashing
        salt_hash = hashlib.sha256(self.base_salt.encode()).hexdigest()[:16]
        return salt_hash

    def _get_hashids_instance(self) -> Hashids:
        """
        Get a Hashids instance with current dynamic salt.

        Returns:
            Hashids instance with current salt
        """
        current_salt = self._generate_dynamic_salt()

        return Hashids(
            salt=current_salt,
            min_length=self.min_length,
            alphabet="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",
        )

    def encode_id(self, entity_id: int, entity_type: str) -> str:
        """
        Encode an entity ID with dynamic salt.

        Args:
            entity_id: The integer ID to encode
            entity_type: Type of entity ('assessment', 'skill', 'session')

        Returns:
            Encoded hash string
        """
        try:
            if not isinstance(entity_id, int) or entity_id <= 0:
                raise ValueError(
                    f"Invalid entity_id: {entity_id}. Must be a positive integer."
                )

            if entity_type not in ["assessment", "skill", "session"]:
                raise ValueError(f"Invalid entity_type: {entity_type}")

            hashids_instance = self._get_hashids_instance()

            # Add entity type as a number to make hashes unique per type
            type_mapping = {"assessment": 1, "skill": 2, "session": 3}
            type_number = type_mapping[entity_type]

            # Encode with both ID and type for uniqueness
            encoded = hashids_instance.encode(entity_id, type_number)

            debug(f"Encoded {entity_type} ID {entity_id} to {encoded}")
            return encoded

        except Exception as e:
            warning(f"Error encoding {entity_type} ID {entity_id}: {str(e)}")
            raise

    def encode_assessment_id(self, assessment_id: int) -> str:
        """Encode assessment ID."""
        return self.encode_id(assessment_id, "assessment")

    def encode_skill_id(self, skill_id: int) -> str:
        """Encode skill ID."""
        return self.encode_id(skill_id, "skill")

    def encode_session_code(self, session_code: str) -> str:
        """Encode session code (6-digit string) instead of session ID."""
        try:
            if (
                not isinstance(session_code, str)
                or len(session_code) != 6
                or not session_code.isdigit()
            ):
                raise ValueError(
                    f"Invalid session_code: {session_code}. Must be a 6-digit string."
                )

            # Convert session code to integer for encoding
            session_code_int = int(session_code)
            hashids_instance = self._get_hashids_instance()

            # Add entity type as a number to make hashes unique per type
            type_number = 3  # session type

            # Encode with both session code and type for uniqueness
            encoded = hashids_instance.encode(session_code_int, type_number)

            debug(f"Encoded session code {session_code} to {encoded}")
            return encoded

        except Exception as e:
            warning(f"Error encoding session code {session_code}: {str(e)}")
            raise


# Global instance for the application
hashid_encoder = DynamicHashID()


def encode_assessment_id(assessment_id: int) -> str:
    """Global function to encode assessment ID."""
    return hashid_encoder.encode_assessment_id(assessment_id)


def encode_skill_id(skill_id: int) -> str:
    """Global function to encode skill ID."""
    return hashid_encoder.encode_skill_id(skill_id)


def encode_session_code(session_code: str) -> str:
    """Global function to encode session code."""
    return hashid_encoder.encode_session_code(session_code)


def _try_decode_with_recent_salts(
    hash_string: str,
    entity_type: str,
    base_salt: str = "herbit_quiz_system",
    min_length: int = 8,
) -> Optional[int]:
    """
    Try to decode a hash using static salt.
    """
    if not hash_string or not isinstance(hash_string, str):
        return None

    type_mapping = {"assessment": 1, "skill": 2, "session": 3}
    expected_type = type_mapping.get(entity_type)

    if expected_type is None:
        return None

    try:
        # Use static salt for consistent decoding
        salt_hash = hashlib.sha256(base_salt.encode()).hexdigest()[:16]

        hashids_instance = Hashids(
            salt=salt_hash,
            min_length=min_length,
            alphabet="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",
        )

        decoded = hashids_instance.decode(hash_string)
        if decoded and len(decoded) >= 2:
            entity_id, type_number = decoded[0], decoded[1]
            if type_number == expected_type:
                debug(
                    f"Successfully decoded {entity_type} hash {hash_string} to ID {entity_id}"
                )
                return entity_id
            else:
                # It's a valid hash but wrong type - log as debug instead of warning
                type_mapping = {1: "assessment", 2: "skill", 3: "session"}
                actual_type = type_mapping.get(type_number, "unknown")
                debug(
                    f"Hash {hash_string} is a valid {actual_type} hash, not a {entity_type} hash"
                )
                return None
    except Exception as e:
        debug(f"Failed to decode: {str(e)}")

    warning(f"Failed to decode {entity_type} hash: {hash_string}")
    return None


def decode_assessment_id(hash_string: str) -> Optional[int]:
    """Decode assessment hash using recent salts."""
    return _try_decode_with_recent_salts(hash_string, "assessment")


def decode_skill_id(hash_string: str) -> Optional[int]:
    """Decode skill hash using recent salts."""
    return _try_decode_with_recent_salts(hash_string, "skill")


def decode_session_code(hash_string: str) -> Optional[str]:
    """Decode session hash to get 6-digit session code."""
    debug(f"Attempting to decode session hash: '{hash_string}'")
    decoded_int = _try_decode_with_recent_salts(hash_string, "session")
    debug(f"Decoded int from hash '{hash_string}': {decoded_int}")
    if decoded_int:
        # Convert back to 6-digit string with leading zeros
        result = str(decoded_int).zfill(6)
        debug(f"Final decoded session code: '{result}'")
        return result
    warning(f"Failed to decode session hash: '{hash_string}'")
    return None


def detect_hash_type(hash_string: str) -> Optional[str]:
    """
    Detect what type of entity a hash represents.

    Args:
        hash_string: The hash to analyze

    Returns:
        'assessment', 'skill', 'session', or None if not decodable
    """
    if not hash_string or not isinstance(hash_string, str):
        return None

    base_salt = "herbit_quiz_system"
    min_length = 8

    try:
        # Use static salt for consistent decoding
        salt_hash = hashlib.sha256(base_salt.encode()).hexdigest()[:16]

        hashids_instance = Hashids(
            salt=salt_hash,
            min_length=min_length,
            alphabet="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",
        )

        decoded = hashids_instance.decode(hash_string)
        if decoded and len(decoded) >= 2:
            type_number = decoded[1]
            type_mapping = {1: "assessment", 2: "skill", 3: "session"}
            return type_mapping.get(type_number)
    except Exception:
        pass

    return None
