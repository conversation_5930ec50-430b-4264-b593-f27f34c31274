/**
 * Composable for managing the skills list page
 */
import { ref, computed, watch } from 'vue';
import { api } from '@/services/api';
import { getErrorMessage, logError } from '@/utils/errorHandling';
import { useMessageHandler } from '@/utils/messageHandler';
import { extractResponseData, extractErrorInfo, extractPaginationMeta } from '@/utils/apiResponseHandler';
import { useLoadingState, useSearch, usePagination } from './useUIState';

export function useSkillsList() {
  const loadingState = useLoadingState(true);
  const search = useSearch({ minLength: 2, debounceMs: 300 });
  const pagination = usePagination({ itemsPerPage: 7, initialPage: 1 });
  const { message, isSuccess, setErrorMessage, clearMessage } = useMessageHandler();

  const skills = ref([]);
  const allSkills = ref([]);

  // Aliases
  const { isLoading } = loadingState;
  const { searchQuery, isSearching } = search;
  const { totalItems, totalPages, setTotalItems } = pagination;

  // Computed properties
  const filteredSkills = computed(() => {
    if (!searchQuery.value || searchQuery.value.trim() === '') {
      return skills.value;
    }
    const query = searchQuery.value.toLowerCase().trim();
    const dataToFilter = allSkills.value.length > 0 ? allSkills.value : skills.value;
    return dataToFilter.filter((skill) => {
      if (!skill) return false;
      const nameMatch = skill.name && skill.name.toLowerCase().includes(query);
      const descriptionMatch = skill.description && skill.description.toLowerCase().includes(query);
      return nameMatch || descriptionMatch;
    });
  });

  const paginatedSkills = computed(() => {
    if (!searchQuery.value || searchQuery.value.trim() === '') {
      return skills.value;
    }
    const startIndex = (pagination.currentPage.value - 1) * pagination.itemsPerPage;
    const endIndex = startIndex + pagination.itemsPerPage;
    return filteredSkills.value.slice(startIndex, endIndex);
  });

  const displayTotalPages = computed(() => {
    if (!searchQuery.value || searchQuery.value.trim() === '') {
      return totalPages.value;
    }
    return Math.ceil(filteredSkills.value.length / pagination.itemsPerPage);
  });

  // Methods
  const fetchSkills = async () => {
    isLoading.value = true;
    clearMessage();
    try {
      const offset = (pagination.currentPage.value - 1) * pagination.itemsPerPage;
      const params = {
        limit: pagination.itemsPerPage,
        offset: offset,
      };
      const response = await api.admin.getSkills(params);
      const skillsData = extractResponseData(response);
      const paginationMeta = extractPaginationMeta(response);
      if (skillsData) {
        skills.value = skillsData;
      }
      if (paginationMeta) {
        setTotalItems(paginationMeta.total);
      }
    } catch (error) {
      logError(error, 'fetchSkills');
      const errorInfo = extractErrorInfo(error);
      setErrorMessage(errorInfo.message || 'An unexpected error occurred while fetching skills');
    } finally {
      isLoading.value = false;
    }
  };

  const fetchAllSkills = async () => {
    try {
      let allData = [];
      let offset = 0;
      const limit = 100;
      let hasMore = true;
      while (hasMore) {
        const params = { limit, offset };
        const response = await api.admin.getSkills(params);
        const skillsData = extractResponseData(response);
        const paginationMeta = extractPaginationMeta(response);
        if (skillsData && skillsData.length > 0) {
          allData = allData.concat(skillsData);
          if (paginationMeta && paginationMeta.total) {
            hasMore = offset + limit < paginationMeta.total;
          } else {
            hasMore = skillsData.length === limit;
          }
          offset += limit;
        } else {
          hasMore = false;
        }
      }
      allSkills.value = allData;
    } catch (error) {
      logError(error, 'fetchAllSkills');
      allSkills.value = [];
    }
  };

  const onPageChange = (page) => {
    pagination.currentPage.value = page;
    if (!searchQuery.value) {
      fetchSkills();
    }
  };

  watch(searchQuery, async (newQuery) => {
    pagination.currentPage.value = 1;
    if (newQuery && newQuery.trim() !== '') {
      if (allSkills.value.length === 0) {
        await fetchAllSkills();
      }
      isSearching.value = true;
    } else {
      isSearching.value = false;
      await fetchSkills();
    }
  });

  return {
    isLoading,
    message,
    isSuccess,
    searchQuery,
    isSearching,
    skills,
    paginatedSkills,
    displayTotalPages,
    currentPage: pagination.currentPage,
    totalItems,
    itemsPerPage: pagination.itemsPerPage,
    fetchSkills,
    onPageChange,
  };
}