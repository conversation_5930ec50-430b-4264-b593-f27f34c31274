<template>
  <svg
    :class="computedClass"
    :style="style"
    stroke="currentColor"
  >
    <use :xlink:href="`/src/assets/svg/sprite.svg#icon-${name}`" />
  </svg>
</template>

<script>
export default {
  name: "SvgIcon",
  props: {
    name: {
      type: String,
      required: true,
    },
    size: {
      type: String,
      default: "md",
    },
    class: {
      type: String,
      default: "",
    },
    style: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    computedClass() {
      const sizeClasses = {
        xs: "h-3 w-3",
        sm: "h-4 w-4",
        md: "h-5 w-5",
        lg: "h-6 w-6",
        xl: "h-8 w-8",
        "2xl": "h-10 w-10",
        "3xl": "h-12 w-12",
      };

      return `${sizeClasses[this.size] || sizeClasses.md} ${this.class}`;
    },
  },
};
</script>
