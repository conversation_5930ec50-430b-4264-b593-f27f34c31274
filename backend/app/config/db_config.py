"""
Configuration module for database settings.

Loads environment variables using dotenv and sets up DATABASE_CONFIG.
Provides connection pooling for better performance.
"""

import os

import psycopg2
import psycopg2.pool
from dotenv import load_dotenv

from ..utils.logger import info as log_info
from ..utils.logger import log_database_error

load_dotenv()

# Basic database configuration
DATABASE_CONFIG = {
    "user": os.getenv("PG_USER"),
    "password": os.getenv("PG_PASSWORD"),
    "dbname": os.getenv("PG_DATABASE"),
    "host": os.getenv("PG_HOST"),
    "port": int(os.getenv("PG_PORT", "5432")),
}

# Create a connection pool
MIN_CONNECTIONS = int(os.getenv("DB_MIN_CONNECTIONS", "1"))
MAX_CONNECTIONS = int(os.getenv("DB_MAX_CONNECTIONS", "10"))

# Initialize the connection pool
connection_pool = None


async def get_connection_pool():
    """Get or create the database connection pool with validation"""
    global connection_pool
    if connection_pool is None:
        try:
            log_info(
                "Initializing database connection pool",
                min_connections=MIN_CONNECTIONS,
                max_connections=MAX_CONNECTIONS,
            )

            connection_pool = psycopg2.pool.SimpleConnectionPool(
                MIN_CONNECTIONS, MAX_CONNECTIONS, **DATABASE_CONFIG
            )

            # Test connection
            conn = connection_pool.getconn()
            try:
                with conn.cursor() as cur:
                    cur.execute("SELECT 1")
                log_info("Database connection pool initialized successfully")
            finally:
                connection_pool.putconn(conn)
        except Exception as e:
            log_database_error("initialize_pool", "connection_pool", e)
            raise
    return connection_pool
